import unittest
from unittest.mock import patch, mock_open, MagicMock
import os
import json
import tempfile
import shutil

from src.processing.markdown_processor_service import LL<PERSON>rivenMarkdownProcessorService


class TestLLMDrivenMarkdownProcessorService(unittest.TestCase):

    def setUp(self):
        """Set up test fixtures with temporary files for schemas."""
        # Create temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        
        # Create mock schema files
        self.narrative_schema_path = os.path.join(self.test_dir, "narrative_schema.json")
        self.dnd5e_schema_path = os.path.join(self.test_dir, "dnd5e_schema.json")
        
        # Mock narrative schema
        narrative_schema = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "definitions": {
                "adventure_yaml": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "entity_type": {"const": "adventure"},
                        "name": {"type": "string"},
                        "game_system": {"type": "string"}
                    },
                    "required": ["id", "entity_type", "name", "game_system"]
                }
            }
        }
        
        # Mock D&D 5e schema
        dnd5e_schema = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "definitions": {
                "monster_dnd5e_yaml": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "entity_type": {"const": "monster"},
                        "name": {"type": "string"},
                        "game_system": {"const": "dnd5e"},
                        "size": {"type": "string"},
                        "challenge_rating": {"type": "string"}
                    },
                    "required": ["id", "entity_type", "name", "game_system", "size", "challenge_rating"]
                }
            }
        }
        
        # Write schema files
        with open(self.narrative_schema_path, 'w') as f:
            json.dump(narrative_schema, f)
        with open(self.dnd5e_schema_path, 'w') as f:
            json.dump(dnd5e_schema, f)

    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.test_dir)

    def test_initialization_loads_schemas_and_instructions(self):
        """Test that the service initializes correctly and loads schemas and instructions."""
        processor = LLMDrivenMarkdownProcessorService(
            self.narrative_schema_path,
            self.dnd5e_schema_path,
            output_root=self.test_dir
        )

        # Verify schemas are loaded
        self.assertIsNotNone(processor.narrative_schema)
        self.assertIsNotNone(processor.dnd5e_schema)
        self.assertIn("dnd5e", processor.game_schemas)
        self.assertEqual(processor.game_schemas["dnd5e"], processor.dnd5e_schema)

        # Verify instructions are initialized (should contain our D&D 5e instructions)
        self.assertIn("dnd5e", processor.llm_instructions)
        self.assertIsInstance(processor.llm_instructions["dnd5e"], dict)
        # Check that some expected instructions are loaded
        if processor.llm_instructions["dnd5e"]:  # If file exists and was loaded
            self.assertIn("monster", processor.llm_instructions["dnd5e"])
            self.assertIn("spell", processor.llm_instructions["dnd5e"])

    def test_is_game_system_supported(self):
        """Test the game system support checking method."""
        processor = LLMDrivenMarkdownProcessorService(
            self.narrative_schema_path,
            self.dnd5e_schema_path,
            output_root=self.test_dir
        )
        
        # Test narrative entities (should always be supported)
        self.assertTrue(processor._is_game_system_supported("dnd5e", "adventure"))
        self.assertTrue(processor._is_game_system_supported("unknown_system", "npc"))
        self.assertTrue(processor._is_game_system_supported("pf2e", "location"))
        
        # Test D&D 5e specific entities
        self.assertTrue(processor._is_game_system_supported("dnd5e", "monster"))
        self.assertTrue(processor._is_game_system_supported("dnd5e", "spell"))
        
        # Test unsupported combinations
        self.assertFalse(processor._is_game_system_supported("pf2e", "monster"))
        self.assertFalse(processor._is_game_system_supported("unknown_system", "spell"))

    @patch('builtins.open', new_callable=mock_open)
    def test_load_game_instructions_success(self, mock_file_open):
        """Test successful loading of game instructions."""
        mock_instructions = {
            "monster": "Extract monster stats",
            "spell": "Extract spell details"
        }
        mock_file_open.return_value.read.return_value = json.dumps(mock_instructions)
        
        processor = LLMDrivenMarkdownProcessorService(
            self.narrative_schema_path,
            self.dnd5e_schema_path,
            output_root=self.test_dir
        )
        
        # Manually call the method to test it
        processor._load_game_instructions("test_system")
        
        self.assertIn("test_system", processor.llm_instructions)
        self.assertEqual(processor.llm_instructions["test_system"], mock_instructions)

    def test_load_game_instructions_file_not_found(self):
        """Test handling of missing instruction files."""
        processor = LLMDrivenMarkdownProcessorService(
            self.narrative_schema_path,
            self.dnd5e_schema_path,
            output_root=self.test_dir
        )
        
        # Call with a non-existent system
        processor._load_game_instructions("nonexistent_system")
        
        # Should create empty instructions
        self.assertIn("nonexistent_system", processor.llm_instructions)
        self.assertEqual(processor.llm_instructions["nonexistent_system"], {})


if __name__ == '__main__':
    unittest.main()
